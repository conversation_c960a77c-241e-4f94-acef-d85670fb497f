import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Params } from 'nestjs-pino';
import pino from 'pino';

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  level: string;
  environment: string;
  enableLogtail: boolean;
  logtailToken?: string;
  logtailIngestingHost?: string;
  prettyPrint: boolean;
  redact: string[];
}

/**
 * Logger configuration service for Pino with Betterstack Logtail integration
 */
@Injectable()
export class LoggerConfigService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * Get logger configuration based on environment
   */
  getLoggerConfig(): LoggerConfig {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    const isProduction = environment === 'production';
    const isTest = environment === 'test';

    return {
      level: this.configService.get<string>('LOG_LEVEL', isTest ? 'error' : isProduction ? 'info' : 'debug'),
      environment,
      enableLogtail: isProduction && !!this.configService.get<string>('LOGTAIL_TOKEN'),
      logtailToken: this.configService.get<string>('LOGTAIL_TOKEN'),
      logtailIngestingHost: this.configService.get<string>('LOGTAIL_INGESTING_HOST'),
      prettyPrint: !isProduction && !isTest,
      redact: [
        'password',
        'token',
        'authorization',
        'cookie',
        'secret',
        'key',
        'req.headers.authorization',
        'req.headers.cookie',
        'res.headers["set-cookie"]',
      ],
    };
  }

  /**
   * Create Pino logger options for NestJS
   */
  createPinoOptions(): Params {
    const config = this.getLoggerConfig();

    // Base configuration for pinoHttp
    const pinoHttpOptions: any = {
      level: config.level,
      redact: config.redact,
      serializers: {
        req: (req: any) => ({
          id: req.id,
          method: req.method,
          url: req.url,
          headers: {
            host: req.headers?.host,
            'user-agent': req.headers?.['user-agent'],
            'content-type': req.headers?.['content-type'],
          },
          remoteAddress: req.remoteAddress,
          remotePort: req.remotePort,
        }),
        res: (res: any) => ({
          statusCode: res.statusCode,
          headers: {
            'content-type': res.headers?.['content-type'],
            'content-length': res.headers?.['content-length'],
          },
        }),
        err: pino.stdSerializers.err,
      },
      formatters: {
        level: (label: string) => ({ level: label }),
        log: (object: any) => ({
          ...object,
          environment: config.environment,
          service: 'pharmacy-backend',
          timestamp: new Date().toISOString(),
        }),
      },
      autoLogging: true,
      quietReqLogger: config.environment === 'test',
      customLogLevel: (req: any, res: any, err: any) => {
        if (res.statusCode >= 400 && res.statusCode < 500) {
          return 'warn';
        } else if (res.statusCode >= 500 || err) {
          return 'error';
        }
        return 'info';
      },
      customSuccessMessage: (req: any, res: any) => {
        return `${req.method} ${req.url} - ${res.statusCode}`;
      },
      customErrorMessage: (req: any, res: any, err: any) => {
        return `${req.method} ${req.url} - ${res.statusCode} - ${err.message}`;
      },
      customAttributeKeys: {
        req: 'request',
        res: 'response',
        err: 'error',
        responseTime: 'duration',
      },
    };

    // Configure transport based on environment
    if (config.prettyPrint) {
      // Development: use pino-pretty for readable logs
      pinoHttpOptions.transport = {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'yyyy-mm-dd HH:MM:ss',
          ignore: 'pid,hostname',
          singleLine: false,
          hideObject: false,
        },
      };
    } else if (config.enableLogtail && config.logtailToken) {
      // Production with Logtail: use multiple targets with file rotation
      pinoHttpOptions.transport = {
        targets: [
          {
            target: 'pino-roll',
            level: config.level,
            options: {
              file: './logs/app.log',
              frequency: 'daily',
              size: '10m',
              mkdir: true,
            },
          },
          {
            target: '@logtail/pino',
            level: config.level,
            options: {
              sourceToken: config.logtailToken,
              ...(config.logtailIngestingHost && {
                endpoint: config.logtailIngestingHost
              }),
            },
          },
        ],
      };

      // Remove formatters when using multiple targets to avoid conflicts
      delete pinoHttpOptions.formatters;
    } else {
      // Production without Logtail: use file rotation + stdout
      pinoHttpOptions.transport = {
        targets: [
          {
            target: 'pino/file',
            level: config.level,
            options: { destination: 1 }, // stdout
          },
          {
            target: 'pino-roll',
            level: config.level,
            options: {
              file: './logs/app.log',
              frequency: 'daily',
              size: '10m',
              mkdir: true,
            },
          },
        ],
      };

      // Remove formatters when using multiple targets
      delete pinoHttpOptions.formatters;
    }

    return {
      pinoHttp: pinoHttpOptions,
    };
  }

  /**
   * Create standalone Pino logger instance
   */
  createStandaloneLogger(context?: string): pino.Logger {
    const config = this.getLoggerConfig();

    const baseOptions: pino.LoggerOptions = {
      level: config.level as pino.Level,
      redact: config.redact,
      base: {
        service: 'pharmacy-backend',
        environment: config.environment,
        context: context || 'Application',
      },
      serializers: {
        err: pino.stdSerializers.err,
      },
      formatters: {
        level: (label: string) => ({ level: label }),
        log: (object: any) => ({
          ...object,
          timestamp: new Date().toISOString(),
        }),
      },
    };

    // Development: use pino-pretty
    if (config.prettyPrint) {
      return pino({
        ...baseOptions,
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'yyyy-mm-dd HH:MM:ss',
            ignore: 'pid,hostname',
          },
        },
      });
    }

    // Production: use Logtail and file rotation if configured
    if (config.enableLogtail && config.logtailToken) {
      // Remove formatters when using multiple targets
      const { formatters, ...optionsWithoutFormatters } = baseOptions;

      return pino({
        ...optionsWithoutFormatters,
        transport: {
          targets: [
            {
              target: 'pino-roll',
              level: config.level,
              options: {
                file: './logs/app.log',
                frequency: 'daily',
                size: '10m',
                mkdir: true,
              },
            },
            {
              target: '@logtail/pino',
              level: config.level,
              options: {
                sourceToken: config.logtailToken,
                ...(config.logtailIngestingHost && {
                  endpoint: config.logtailIngestingHost
                }),
              },
            },
          ],
        },
      });
    }

    // Production without Logtail: use file rotation + stdout
    const { formatters, ...optionsWithoutFormatters } = baseOptions;

    return pino({
      ...optionsWithoutFormatters,
      transport: {
        targets: [
          {
            target: 'pino/file',
            level: config.level,
            options: { destination: 1 }, // stdout
          },
          {
            target: 'pino-roll',
            level: config.level,
            options: {
              file: './logs/app.log',
              frequency: 'daily',
              size: '10m',
              mkdir: true,
            },
          },
        ],
      },
    });
  }
}

/**
 * Factory function to create logger options
 */
export const createLoggerOptions = (configService: ConfigService): Params => {
  const loggerConfigService = new LoggerConfigService(configService);
  return loggerConfigService.createPinoOptions();
};

/**
 * Factory function to create standalone logger
 */
export const createStandaloneLogger = (configService: ConfigService, context?: string): pino.Logger => {
  const loggerConfigService = new LoggerConfigService(configService);
  return loggerConfigService.createStandaloneLogger(context);
};
